declare -gx REPLIT_LD_AUDIT=/nix/store/6y0zqxaf220r36b74hwsq9m2b2av3lw7-replit_rtld_loader-1/rtld_loader.so
declare -gx LOCALE_ARCHIVE=/usr/lib/locale/locale-archive
declare -gx REPL_LANGUAGE=nix
declare -gx GLIBC_TUNABLES=glibc.rtld.optional_static_tls=10000
declare -gx PKG_CONFIG_PATH_FOR_TARGET=/nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/lib/pkgconfig:/nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/lib/pkgconfig
declare -gx REPLIT_CONTAINER=gcr.io/marine-cycle-160323/repl-base:97433c06ca04972a1619dedd971cb3a93f4a5087
declare -gx REPLIT_BASHRC=/nix/store/vgmsjrn5bbcdwgqxy27njrq6n8scc2nc-replit-bashrc/bashrc
declare -gx USER=runner
declare -gx REPLIT_RIPPKGS_INDICES=/nix/store/sic31kz2lvpf4v5wlaldrla7ivz4v2j2-rippkgs-indices
declare -gx NIX_PS1='\[\033[01;34m\]\w\[\033[00m\]\$ '
declare -gx NIXPKGS_ALLOW_UNFREE=1
declare -gx NIX_PROFILES='/nix/var/nix/profiles/default /home/<USER>/.nix-profile'
declare -gx PGDATABASE=heliumdb
declare -gx XDG_DATA_HOME=/home/<USER>/workspace/.local/share
declare -gx REPLIT_CONNECTORS_HOSTNAME=connectors.replit.com
declare -gx NIX_CFLAGS_COMPILE='-isystem /nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/include -isystem /nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/include'
read -r _new_path <<< "/nix/store/7y2nxi5svi3ya06jyfw7l06rxvfb1zzf-jq-1.7.1-bin/bin:/nix/store/infk4071v1difwnn7nsyarsmsj4pjfz8-sqlite-interactive-3.45.3-bin/bin:/nix/store/djy8g4cghlw19fmy6zblim1waxkr7mf2-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/8y4ls7z2sfxbq6ch3yp45l28p29qswvx-nodejs-20.19.3-wrapped/bin:/nix/store/nj22dzxzzx4m80lfa91fzqh8k9lvwigw-bun-1.2.16/bin:/nix/store/pciji08qkvcg3191yykpfipdcbbic9k5-pnpm-10.12.4/bin:/nix/store/6mzbvyzh4dfgfab6lciclji7awp9w3c4-yarn-1.22.22/bin:/nix/store/gvg9vrp821nmnyf9pxig6pss1sjywd5w-prettier-3.5.3/bin:/nix/store/w7ldv9b1vc48a235g7ib2kjyqlrzfv0s-postgresql-16.9/bin:/nix/store/ckxxssz70fkcr1fv5l4qn0mv1a66vc1i-pid1/bin:/nix/store/s7bk3jqgq54wriw50r9n4sip8j627nih-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
#PATH=/nix/store/7y2nxi5svi3ya06jyfw7l06rxvfb1zzf-jq-1.7.1-bin/bin:/nix/store/infk4071v1difwnn7nsyarsmsj4pjfz8-sqlite-interactive-3.45.3-bin/bin:/nix/store/djy8g4cghlw19fmy6zblim1waxkr7mf2-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/8y4ls7z2sfxbq6ch3yp45l28p29qswvx-nodejs-20.19.3-wrapped/bin:/nix/store/nj22dzxzzx4m80lfa91fzqh8k9lvwigw-bun-1.2.16/bin:/nix/store/pciji08qkvcg3191yykpfipdcbbic9k5-pnpm-10.12.4/bin:/nix/store/6mzbvyzh4dfgfab6lciclji7awp9w3c4-yarn-1.22.22/bin:/nix/store/gvg9vrp821nmnyf9pxig6pss1sjywd5w-prettier-3.5.3/bin:/nix/store/w7ldv9b1vc48a235g7ib2kjyqlrzfv0s-postgresql-16.9/bin:/nix/store/ckxxssz70fkcr1fv5l4qn0mv1a66vc1i-pid1/bin:/nix/store/s7bk3jqgq54wriw50r9n4sip8j627nih-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
if [ -e "/run/replit/env/last" ]; then read -r _last_path < <(\grep '^#PATH=' /run/replit/env/last | cut -f 2 -d =); fi
_user_components="$(\tr : $'\n' <<< "${PATH:-}" |\grep -xv -f <(\tr : $'\n' <<< "${_last_path}") |\tr $'\n' :)"
declare -gx PATH="${_user_components}${_new_path}"
declare -gx REPLIT_LD_LIBRARY_PATH=/nix/store/bjf63mfkn3r6svmb4qlsy1v8bff4dwa9-jq-1.7.1-lib/lib:/nix/store/x2d3rk6v352ivd4sbsh5mdd94al4c39l-sqlite-interactive-3.45.3-debug/lib:/nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/lib:/nix/store/a10mpzdphby44za48g1yamhgvjhcwfhm-sqlite-interactive-3.45.3/lib:/nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/lib
declare -gx HOSTNAME=4573c83c1d8d
declare -gx REPL_OWNER_ID=35799047
declare -gx PKG_CONFIG_PATH=/nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/lib/pkgconfig:/nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/lib/pkgconfig
declare -gx PGPORT=5432
declare -gx HOME=/home/<USER>
declare -gx LANG=en_US.UTF-8
declare -gx REPLIT_NIX_CHANNEL=stable-24_05
declare -gx REPLIT_ENVIRONMENT=production
declare -gx REPL_ID=0f867722-47fe-47b2-b2c5-4b785a3cfe64
declare -gx REPL_IMAGE=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx __EGL_VENDOR_LIBRARY_FILENAMES=/nix/store/cpwib3zazj49fm0y04y53w4xkbqsgrgm-mesa-25.0.7/share/glvnd/egl_vendor.d/50_mesa.json
declare -gx REPL_HOME=/home/<USER>/workspace
declare -gx XDG_CONFIG_HOME=/home/<USER>/workspace/.config
declare -gx PORT=5000
declare -gx REPL_SLUG=workspace
declare -gx XDG_DATA_DIRS=/nix/store/5cq70lcqc2aph4q03brijk3alfh5rnmz-jq-1.7.1-doc/share:/nix/store/infk4071v1difwnn7nsyarsmsj4pjfz8-sqlite-interactive-3.45.3-bin/share:/nix/store/shvd454wy3lnhirj922yrzffzscci28s-jq-1.7.1-man/share:/nix/store/s7bk3jqgq54wriw50r9n4sip8j627nih-replit-runtime-path/share
declare -gx REPLIT_CLI=/nix/store/mzrby2ihiw6jrnpy281n1amfyn4y1rkq-pid1-0.0.1/bin/replit
declare -gx REPLIT_DOMAINS=0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev
declare -gx COLORTERM=truecolor
declare -gx GI_TYPELIB_PATH=''
declare -gx REPL_OWNER=mazenobid88
declare -gx PROMPT_DIRTRIM=2
declare -gx REPL_PUBKEYS='{"crosis-ci":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:1":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:latest":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","prod":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:1":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:3":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:4":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:5":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:latest":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","vault-goval-token":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:1":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:latest":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E="}'
declare -gx PGHOST=helium
declare -gx REPLIT_CLUSTER=worf
declare -gx XDG_CACHE_HOME=/home/<USER>/workspace/.cache
declare -gx CONNECTORS_HOSTNAME=connectors.replit.com
declare -gx LDFLAGS='-L/nix/store/bjf63mfkn3r6svmb4qlsy1v8bff4dwa9-jq-1.7.1-lib/lib -L/nix/store/x2d3rk6v352ivd4sbsh5mdd94al4c39l-sqlite-interactive-3.45.3-debug/lib -L/nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/lib -L/nix/store/a10mpzdphby44za48g1yamhgvjhcwfhm-sqlite-interactive-3.45.3/lib -L/nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/lib'
declare -gx CFLAGS='-isystem /nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/include -isystem /nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/include'
declare -gx PGPASSWORD=password
declare -gx NIX_LDFLAGS='-L/nix/store/bjf63mfkn3r6svmb4qlsy1v8bff4dwa9-jq-1.7.1-lib/lib -L/nix/store/x2d3rk6v352ivd4sbsh5mdd94al4c39l-sqlite-interactive-3.45.3-debug/lib -L/nix/store/n5chc8i5za5jrr4biwwgx7a96p0a7aq9-jq-1.7.1-dev/lib -L/nix/store/a10mpzdphby44za48g1yamhgvjhcwfhm-sqlite-interactive-3.45.3/lib -L/nix/store/dlfjfi5j8j73nbvir6lqj16s4q2di31f-sqlite-interactive-3.45.3-dev/lib'
declare -gx LIBGL_DRIVERS_PATH=/nix/store/cpwib3zazj49fm0y04y53w4xkbqsgrgm-mesa-25.0.7/lib/dri
declare -gx REPLIT_PID1_FLAG_PREEVALED_SYSPKGS=1
declare -gx REPLIT_PID1_VERSION=0.0.0-245c1aa
declare -gx REPLIT_SUBCLUSTER=paid
declare -gx GIT_EDITOR=replit-git-editor
declare -gx DATABASE_URL='**************************************************************'
declare -gx REPLIT_DEV_DOMAIN=0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev
declare -gx GIT_ASKPASS=replit-git-askpass
declare -gx REPLIT_RTLD_LOADER=1
declare -gx LD_AUDIT=/nix/store/6y0zqxaf220r36b74hwsq9m2b2av3lw7-replit_rtld_loader-1/rtld_loader.so
declare -gx npm_config_prefix=/home/<USER>/workspace/.config/npm/node_global
declare -gx NIX_PATH=nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels
declare -gx DISPLAY=:0
declare -gx REPLIT_HELIUM_ENABLED=true
declare -gx PGUSER=postgres
declare -gx DOCKER_CONFIG=/home/<USER>/workspace/.config/docker
