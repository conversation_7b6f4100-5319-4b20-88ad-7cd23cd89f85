2025-09-14 15:44:41.844 [info] Extension host with pid 1110 started
2025-09-14 15:44:41.844 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/vscode.lock'
2025-09-14 15:44:41.845 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-09-14 15:44:41.848 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/vscode.lock': The pid 1034 appears to be gone.
2025-09-14 15:44:41.848 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/vscode.lock': Deleting a stale lock.
2025-09-14 15:44:41.889 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/vscode.lock': Lock acquired.
2025-09-14 15:44:42.353 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-09-14 15:44:42.355 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-09-14 15:44:42.355 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescriptreact'
2025-09-14 15:44:42.356 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-09-14 15:44:42.801 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-09-14 15:44:42.802 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-09-14 15:44:43.240 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-09-14 15:44:43.581 [info] Eager extensions activated
2025-09-14 15:44:43.581 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:43.582 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:43.582 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:43.582 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:43.582 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:47.740 [info] ExtensionService#_doActivateExtension vscode.terminal-suggest, startup: false, activationEvent: 'onTerminalShellIntegration:*'
2025-09-14 15:44:52.002 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-09-14 15:44:52.003 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-09-14 15:44:52.003 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-09-14 15:44:58.388 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Csnowb%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at S9e.$tryOpenDocument (vscode-file://vscode-app/d:/visual/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:528:10721)
