2025-09-14 15:44:49.415 [info] 'AugmentConfigListener' settings parsed successfully
2025-09-14 15:44:49.415 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"workspace":{"enumerationTimeoutMs":30000},"advanced":{}}
2025-09-14 15:44:49.415 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"notificationPollingIntervalMs":180000,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"enableViewedContentTracking":false,"viewedContentCloseRangeThreshold":5,"viewedContentDiscreteJumpThreshold":15,"viewedContentMinEventAgeMs":1000,"viewedContentMaxEventAgeMs":3600000,"viewedContentMaxTrackedFiles":10,"viewedContentMaxSameFileEntries":2,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutputAfterTruncation":0,"maxLinesTerminalProcessOutput":0,"truncationFooterAdditionText":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"agentChatModel":"","enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"modelInfoRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryParams":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false,"enableAgentTabs":false,"enableSwarmMode":false,"enableGroupedTools":false,"remoteAgentsResumeHintAvailableTtlDays":0,"enableParallelTools":false,"enableAgentGitTracker":false,"enableNativeRemoteMcp":true,"vscodeTerminalStrategy":"vscode_events","enableSentry":false,"webviewErrorSamplingRate":0,"webviewTraceSamplingRate":0,"agentViewToolParams":"","enableLucideIcons":false,"enableEnhancedDehydrationMode":false,"subscriptionBannerDismissibility":"off","vscodeShowThinkingSummaryMinVersion":"","fraudSignEndpoints":false}
2025-09-14 15:44:49.415 [info] 'AugmentExtension' Retrieving model config
2025-09-14 15:44:49.611 [info] 'AugmentConfigListener' settings parsed successfully
2025-09-14 15:44:49.734 [info] 'AugmentExtension' Retrieved model config
2025-09-14 15:44:49.734 [info] 'AugmentExtension' Returning model config
2025-09-14 15:44:49.778 [info] 'FeatureFlagManager' feature flags changed:
  - notificationPollingIntervalMs: 180000 to 60000
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - enableViewedContentTracking: false to true
  - viewedContentMaxEventAgeMs: 3600000 to 30000
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - truncationFooterAdditionText: "" to "**Only use view-range-untruncated or search-untruncated tools if additional output is strictly necessary to continue**, such as when:\n- You need to find specific error details that are clearly truncated\n- You need to search for specific patterns or text that might be elsewhere in the output\n- The truncated output is genuinely insufficient for the task at hand\n\nIf you do need to use these tools:\n- For view-range-untruncated: Request only the specific line ranges you actually need\n- For search-untruncated: Use specific search terms rather than viewing large ranges\n"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - agentChatModel: "" to "claude-sonnet-4-0-200k-v9-c4-p2-agent"
  - enableModelRegistry: false to true
  - openFileManagerV2Enabled: false to true
  - modelRegistry > Claude Sonnet 4: undefined to "claude-sonnet-4-0-200k-v8-c4-p2-agent"
  - modelRegistry > GPT-5: undefined to "gpt5-med-200k-v7-c4-p2-agent"
  - modelInfoRegistry > claude-sonnet-4-0-200k-v9-c4-p2-agent: undefined to {"description":"Anthropic Claude Sonnet 4","disabled":false,"displayName":"Claude Sonnet 4","shortName":"sonnet4"}
  - modelInfoRegistry > gpt5-responses-med-200k-v8-c4-p2-agent: undefined to {"description":"OpenAI GPT-5","disabled":false,"displayName":"GPT-5","shortName":"gpt5"}
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMinVersion: "" to "0.0.0"
  - historySummaryParams: "" to "{\"buffer_time_before_cache_expiration_ms\": 30000, \"cache_ttl_ms\": 300000, \"history_tail_size_chars_to_exclude\": 100000, \"prompt\": \"Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\\n\\nYour summary should be structured as follows:\\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\\n\\nExample summary structure:\\n1. Previous Conversation:\\n[Detailed description]\\n2. Current Work:\\n[Detailed description]\\n3. Key Technical Concepts:\\n- [Concept 1]\\n- [Concept 2]\\n- [...]\\n4. Relevant Files and Code:\\n- [File Name 1]\\n    - [Summary of why this file is important]\\n    - [Summary of the changes made to this file, if any]\\n    - [Important Code Snippet]\\n- [File Name 2]\\n    - [Important Code Snippet]\\n- [...]\\n5. Problem Solving:\\n[Detailed description]\\n6. Pending Tasks and Next Steps:\\n- [Task 1 details & next steps]\\n- [Task 2 details & next steps]\\n- [...]\\n\\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\\nIMPORTANT: DO NOT CALL ANY TOOLS. JUST OUTPUT THE SUMMARY.\\n\", \"trigger_on_history_size_chars\": 350000, \"trigger_on_history_size_chars_when_cache_expiring\": 350000}"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 2000
  - enableExchangeStorage: false to true
  - enableToolUseStateStorage: false to true
  - retryChatStreamTimeouts: false to true
  - enableMemoryRetrieval: false to true
  - enableAgentTabs: false to true
  - remoteAgentsResumeHintAvailableTtlDays: 0 to 21
  - enableParallelTools: false to true
  - vscodeTerminalStrategy: "vscode_events" to "script_capture"
  - enableSentry: false to true
  - webviewErrorSamplingRate: 0 to 0.1
  - webviewTraceSamplingRate: 0 to 0.1
  - agentViewToolParams: "" to "{ \"max_depth\": 2, \"view_dir_max_entries_per_depth\": [-1, 50]}"
2025-09-14 15:44:49.778 [info] 'SidecarAnalytics' Segment analytics initialized for vscode in tenant i1
2025-09-14 15:44:49.778 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 9/13/2025, 7:38:39 PM
2025-09-14 15:44:49.778 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-09-14 15:44:49.778 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-09-14 15:44:49.778 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 9/13/2025, 7:38:39 PM; type = explicit
2025-09-14 15:44:49.778 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-09-14 15:44:49.778 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 9/13/2025, 7:38:39 PM
2025-09-14 15:44:49.905 [info] 'AugmentConfigListener' settings parsed successfully
2025-09-14 15:44:49.905 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"workspace":{"enumerationTimeoutMs":30000},"advanced":{}}
2025-09-14 15:44:49.905 [info] 'GitInitialization' Git tracking disabled by enableAgentGitTracker feature flag
2025-09-14 15:44:49.905 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-09-14 15:44:49.905 [info] 'NotificationWatcher' Starting notification polling with interval 60000ms
2025-09-14 15:44:49.905 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-09-14 15:44:49.905 [info] 'HotKeyHints' HotKeyHints initialized
2025-09-14 15:44:49.919 [info] 'AugmentExtension' Extension startup completed in 1644ms
2025-09-14 15:44:49.919 [info] 'GitInitialization' Git tracking disabled by enableAgentGitTracker feature flag
2025-09-14 15:44:49.920 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-09-14 15:44:50.023 [warning] 'TerminalProcessTools' Checking if script command is available. Current shell: bash, platform: linux
2025-09-14 15:44:50.024 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-09-14 15:44:50.025 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-09-14 15:44:52.126 [info] 'WorkspaceManager[workspace]' Start tracking
2025-09-14 15:44:52.524 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-09-14 15:44:52.524 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-09-14 15:44:52.532 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-09-14 15:44:52.532 [info] 'OpenFileManager' Opened source folder 100
2025-09-14 15:44:52.551 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-09-14 15:44:53.306 [info] 'MtimeCache[workspace]' read 18348 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-09-14 15:44:54.891 [info] 'GitInitialization' Git tracking disabled by enableAgentGitTracker feature flag
2025-09-14 15:44:57.216 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250914T154418/exthost2/output_logging_20250914T154441
2025-09-14 15:44:57.247 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-09-14 15:44:57.248 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-09-14 15:44:57.248 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-09-14 15:44:57.248 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-09-14 15:44:57.248 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2157 msec late.
2025-09-14 15:45:01.373 [error] 'AugmentExtensionSidecar' API request 6652d4d9-bdaa-4a1e-9009-83dfa47cef85 to https://i1.api.augmentcode.com/record-session-events response 502: Bad Gateway
2025-09-14 15:45:02.197 [error] 'AugmentExtensionSidecar' API request 539d4326-7356-4453-829a-66db7070edf3 to https://i1.api.augmentcode.com/report-error response 502: Bad Gateway
2025-09-14 15:45:02.198 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus unavailable" due to error: HTTP error: 502 Bad Gateway
2025-09-14 15:45:02.198 [error] 'ExtensionSessionEventReporter' Error uploading metrics: Error: HTTP error: 502 Bad Gateway Error: HTTP error: 502 Bad Gateway
    at Function.fromResponse (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:403:18966)
    at MW.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:657:2257)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at MW.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:659:66367)
    at MW.logExtensionSessionEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:659:39276)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:405:11946
    at Bs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:405:9834)
    at e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:405:11845)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/extension.js:405:11168
2025-09-14 15:45:02.198 [info] 'ExtensionSessionEventReporter' Operation failed with error Error: HTTP error: 502 Bad Gateway, retrying in 100 ms; retries = 0
2025-09-14 15:45:02.627 [info] 'ExtensionSessionEventReporter' Operation succeeded after 1 transient failures
2025-09-14 15:45:16.794 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-09-14 15:45:17.793 [warning] 'MainPanelWebviewProvider' Timeout waiting for feature flags after 1000ms, proceeding with default flags
2025-09-14 15:45:19.018 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-09-14 15:45:19.018 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-09-14 15:45:19.026 [info] 'TaskManager' Setting current root task UUID to 1d60d0ce-d716-49bc-a63c-3ccb963f812a
2025-09-14 15:45:19.190 [info] 'DynamicLevelKvStore' Ensuring LevelDB is initialized
2025-09-14 15:45:32.753 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-09-14 15:45:32.979 [info] 'TaskManager' Setting current root task UUID to 03f7ca07-26ef-4213-97aa-a1142a27a7cf
2025-09-14 15:45:32.980 [info] 'TaskManager' Setting current root task UUID to 03f7ca07-26ef-4213-97aa-a1142a27a7cf
2025-09-14 15:45:40.470 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-09-14 15:45:40.471 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 2600
  - files emitted: 18626
  - other paths emitted: 470
  - total paths emitted: 21696
  - timing stats:
    - readDir: 110 ms
    - filter: 610 ms
    - yield: 104 ms
    - total: 914 ms
2025-09-14 15:45:40.471 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 18483
  - paths not accessible: 0
  - not plain files: 0
  - large files: 95
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 18339
  - mtime cache misses: 144
  - probe batches: 21
  - blob names probed: 18537
  - files read: 353
  - blobs uploaded: 58
  - timing stats:
    - ingestPath: 32 ms
    - probe: 38975 ms
    - stat: 157 ms
    - read: 7454 ms
    - upload: 2172 ms
2025-09-14 15:45:40.471 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 426 ms
  - read MtimeCache: 755 ms
  - pre-populate PathMap: 321 ms
  - create PathFilter: 339 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 919 ms
  - purge stale PathMap entries: 4 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 45566 ms
  - enable persist: 14 ms
  - total: 48345 ms
2025-09-14 15:45:40.471 [info] 'WorkspaceManager' Workspace startup complete in 50705 ms
2025-09-14 15:47:37.214 [error] 'AugmentExtensionSidecar' API request d69fe7ec-8da5-493f-82ab-fa6201cd60ac to https://i1.api.augmentcode.com/find-missing failed: The operation was aborted due to timeout
2025-09-14 15:47:37.522 [info] 'DiskFileManager[workspace]' Operation failed with error Error: The operation was aborted due to timeout, retrying in 100 ms; retries = 0
2025-09-14 15:47:37.875 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-09-14 15:47:58.277 [error] 'AugmentExtensionSidecar' API request 402118c4-96f3-458e-8c71-03ab4d8adcdb to https://i1.api.augmentcode.com/find-missing failed: The operation was aborted due to timeout
2025-09-14 15:47:58.607 [info] 'DiskFileManager[workspace]' Operation failed with error Error: The operation was aborted due to timeout, retrying in 100 ms; retries = 0
2025-09-14 15:47:58.988 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-09-14 15:55:40.333 [error] 'AugmentExtensionSidecar' API request f5403ab2-62a9-4bd6-995d-276625afacb4 to https://i1.api.augmentcode.com/find-missing failed: The operation was aborted due to timeout
2025-09-14 15:55:40.667 [info] 'DiskFileManager[workspace]' Operation failed with error Error: The operation was aborted due to timeout, retrying in 100 ms; retries = 0
2025-09-14 15:55:41.102 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
