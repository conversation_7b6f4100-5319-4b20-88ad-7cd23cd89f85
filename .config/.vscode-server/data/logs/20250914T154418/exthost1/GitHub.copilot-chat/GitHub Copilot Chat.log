2025-09-14 15:44:31.125 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-09-14 15:44:31.125 [info] Can't use the Electron fetcher in this environment.
2025-09-14 15:44:31.125 [info] Using the Node fetch fetcher.
2025-09-14 15:44:31.125 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-09-14 15:44:31.125 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-09-14 15:44:31.125 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-09-14 15:44:33.341 [info] GithubAvailableEmbeddingTypesManager: Got embeddings. Primary: text-embedding-3-small-512,metis-1024-I16-Binary. Deprecated: 
2025-09-14 15:44:33.345 [info] GithubAvailableEmbeddingTypesManager: Got embeddings. Primary: text-embedding-3-small-512,metis-1024-I16-Binary. Deprecated: 
2025-09-14 15:44:33.345 [info] WorkspaceChunkSearchService: using embedding type text-embedding-3-small-512
2025-09-14 15:44:33.461 [info] Logged in as mazen91111
2025-09-14 15:44:34.000 [info] FetcherService: node-fetch succeeded
2025-09-14 15:44:34.000 [info] FetcherService: node-fetch succeeded
2025-09-14 15:44:34.001 [info] Got Copilot token for mazen91111
2025-09-14 15:44:34.010 [info] activationBlocker from 'languageModelAccess' took for 3944ms
2025-09-14 15:44:34.208 [info] Fetched model metadata in 4128ms ae055879-347e-4f32-9ad8-bbe229211986
2025-09-14 15:44:34.222 [info] Latest entry: ccreq:latest.copilotmd
2025-09-14 15:44:34.222 [info] ccreq:d83a9009.copilotmd | markdown
2025-09-14 15:44:34.896 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-09-14 15:44:34.896 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-09-14 15:44:34.913 [info] Registering default platform agent...
2025-09-14 15:44:34.914 [info] activationBlocker from 'conversationFeature' took for 4851ms
2025-09-14 15:44:35.096 [info] BYOK: Copilot Chat known models list fetched successfully.
