2025-09-14 15:44:22.391 [info] Extension host with pid 620 started
2025-09-14 15:44:22.601 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-09-14 15:44:22.799 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-09-14 15:44:23.245 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-09-14 15:44:23.246 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-09-14 15:44:23.515 [info] Eager extensions activated
2025-09-14 15:44:23.515 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:23.515 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:23.516 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:23.516 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:23.516 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-09-14 15:44:32.876 [info] ExtensionService#_doActivateExtension vscode.terminal-suggest, startup: false, activationEvent: 'onTerminalShellIntegration:*'
2025-09-14 15:44:35.322 [info] Extension host terminating: received terminate message from renderer
2025-09-14 15:44:35.382 [info] Extension host with pid 620 exiting with code 0
