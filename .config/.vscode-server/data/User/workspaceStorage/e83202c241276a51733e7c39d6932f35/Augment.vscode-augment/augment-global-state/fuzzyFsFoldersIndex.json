{"/home/<USER>/workspace/": {"rootPath": "/home/<USER>/workspace", "relPath": ""}, "/home/<USER>/workspace/plugins/web-dashboard/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/web-dashboard/"}, "/home/<USER>/workspace/client/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/"}, "/home/<USER>/workspace/client/src/pages/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/"}, "/home/<USER>/workspace/client/src/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/"}, "/home/<USER>/workspace/client/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/"}, "/home/<USER>/workspace/client/src/components/ui/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/"}, "/home/<USER>/workspace/.local/state/replit/agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.31.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.31.0/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.31.0/l10n/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.31.0/l10n/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.31.0/assets/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.31.0/assets/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.368.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.368.0/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.368.0/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.368.0/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.368.0/assets/status/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.368.0/assets/status/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/win32/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/win32/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/solaris/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/solaris/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/snappy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/snappy/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/snappy/cmake/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/snappy/cmake/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/openbsd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/openbsd/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/mac/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/mac/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/linux/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/linux/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/freebsd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/snappy/freebsd/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/port-libuv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/port-libuv/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/patches/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/patches/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/util/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/table/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/port/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/port/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/port/win/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/port/win/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/issues/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/issues/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/include/leveldb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/include/leveldb/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/helpers/memenv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/helpers/memenv/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/db/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/out/runtime/deps/leveldb/leveldb-1.20/db/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/next-edit/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/next-edit/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/keyboard/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/keyboard/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/keyboard/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/keyboard/light/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/keyboard/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/media/keyboard/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/common-webviews/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/common-webviews/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.549.1/common-webviews/assets/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.549.1/common-webviews/assets/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/"}, "/home/<USER>/workspace/server/": {"rootPath": "/home/<USER>/workspace", "relPath": "server/"}, "/home/<USER>/workspace/plugins/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/"}, "/home/<USER>/workspace/client/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/"}, "/home/<USER>/workspace/client/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/"}, "/home/<USER>/workspace/client/src/components/examples/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/examples/"}, "/home/<USER>/workspace/attached_assets/": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/"}, "/home/<USER>/workspace/.upm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/"}, "/home/<USER>/workspace/.local/state/replit/agent/filesystem/": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/filesystem/"}, "/home/<USER>/workspace/.local/share/": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/share/"}, "/home/<USER>/workspace/.config/.vscode-server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/"}, "/home/<USER>/workspace/.config/.vscode-server/data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/output_logging_20250913T193749/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/output_logging_20250913T193749/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-75786b3b/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-75786b3b/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/workbench/contrib/terminal/common/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/workbench/contrib/terminal/common/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/platform/terminal/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/platform/terminal/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/platform/files/node/watcher/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/platform/files/node/watcher/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/base/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/out/vs/base/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/typescript-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/typescript-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/typescript-language-features/resources/walkthroughs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/typescript-language-features/resources/walkthroughs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/tunnel-forwarding/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/tunnel-forwarding/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/tunnel-forwarding/.vscode/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/tunnel-forwarding/.vscode/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/terminal-suggest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/terminal-suggest/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/simple-browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/simple-browser/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/simple-browser/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/simple-browser/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/search-result/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/search-result/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/search-result/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/search-result/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/references-view/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/references-view/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/php-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/php-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/npm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/npm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/npm/images/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/npm/images/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.vscode-js-profile-table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.vscode-js-profile-table/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/resources/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/resources/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/extension-editing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/extension-editing/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/merge-conflict/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/merge-conflict/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/emmet/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/emmet/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/media-preview/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/media-preview/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/gulp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/gulp/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/resources/icons/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/resources/icons/light/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/resources/icons/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/resources/icons/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/resources/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/resources/light/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ipynb/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ipynb/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/grunt/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/grunt/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug-companion/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug-companion/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/github-authentication/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/github-authentication/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/github/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git-base/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git-base/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/server/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/server/lib/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/github-authentication/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/github-authentication/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/json-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/json-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.vscode-js-profile-table/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.vscode-js-profile-table/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/media-preview/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/media-preview/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/microsoft-authentication/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/microsoft-authentication/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/vendor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/vendor/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/microsoft-authentication/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/microsoft-authentication/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/targets/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/targets/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ipynb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ipynb/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git-base/languages/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git-base/languages/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git-base/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git-base/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/git/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/debug-auto-launch/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/debug-auto-launch/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/debug-server-ready/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/debug-server-ready/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/ui/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/src/ui/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/preview-styles/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/preview-styles/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug-companion/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/ms-vscode.js-debug-companion/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/jake/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/jake/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-math/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/html-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/markdown-language-features/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/debug-auto-launch/.vscode/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/debug-auto-launch/.vscode/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/css-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/css-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/css-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/css-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/configuration-editing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/configuration-editing/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/configuration-editing/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/extensions/configuration-editing/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/bin/remote-cli/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/bin/remote-cli/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/bin/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f220831ea2d946c0dcb0f3eaa480eb435a2c1260/server/bin/helpers/"}, "/home/<USER>/workspace/.cache/typescript/5.6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.6/"}, "/home/<USER>/workspace/.cache/replit/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/"}, "/home/<USER>/workspace/.cache/replit/nix/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/"}, "/home/<USER>/workspace/.cache/replit/modules/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/"}, "/home/<USER>/workspace/.cache/replit/env/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod@3.24.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod@3.24.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod@3.24.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod@3.24.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod@3.24.2@@@1/lib/locales/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod@3.24.2@@@1/lib/locales/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod@3.24.2@@@1/lib/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod@3.24.2@@@1/lib/helpers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod@3.24.2@@@1/lib/benchmarks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod@3.24.2@@@1/lib/benchmarks/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod@3.24.2@@@1/lib/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod@3.24.2@@@1/lib/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/zod-validation-error@3.4.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/zod-validation-error@3.4.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/yaml@2.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/yaml@2.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/yaml@2.6.0@@@1/browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/yaml@2.6.0@@@1/browser/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/yallist@3.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/yallist@3.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/yallist@2.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/yallist@2.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/xtend@4.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/xtend@4.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ws@8.18.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ws@8.18.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ws@8.18.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ws@8.18.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/wrap-ansi@8.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/wrap-ansi@8.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/wrap-ansi@7.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/wrap-ansi@7.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/wouter@3.3.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/wouter@3.3.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/wouter@3.3.5@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/wouter@3.3.5@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/wouter@3.3.5@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/wouter@3.3.5@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/uid-safe@2.1.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/uid-safe@2.1.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-scale/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-scale/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/public/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/public/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/undici-types@6.19.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/undici-types@6.19.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tiny-invariant@1.3.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tiny-invariant@1.3.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/stubs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/stubs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/which@2.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/which@2.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-scale/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-scale/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/types/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/types/generated/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/vary@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/vary@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/public/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/public/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-voronoi/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-voronoi/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-interpolate/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-interpolate/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-array/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-array/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/vite@5.4.19@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/vite@5.4.19@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/vite@5.4.19@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/vite@5.4.19@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/curve/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/curve/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@4.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@4.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tslib@2.8.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tslib@2.8.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/cs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/cs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tw-animate-css@1.2.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tw-animate-css@1.2.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/vite@5.4.19@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/vite@5.4.19@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-format/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-format/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/shim/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/shim/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/thenify@3.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/thenify@3.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/value-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/value-parser/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tapable@2.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tapable@2.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tslib@2.8.1@@@1/modules/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tslib@2.8.1@@@1/modules/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/build/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/build/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/value-parser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/value-parser/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-format/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-format/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/toidentifier@1.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/toidentifier@1.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/order/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/order/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/to-regex-range@5.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/to-regex-range@5.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/update-browserslist-db@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/update-browserslist-db@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/thenify-all@1.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/thenify-all@1.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/internmap/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/internmap/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-path/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-path/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/vaul@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/vaul@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/tr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/tr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/de/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/de/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/es/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/es/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/type-is@1.6.18@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/type-is@1.6.18@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/postcss-plugins/nesting/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/postcss-plugins/nesting/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-path/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-path/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/es/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/es/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-interpolate/src/transform/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-interpolate/src/transform/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/css/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/css/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/it/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/it/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/fr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/fr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/use-callback-ref@1.3.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/use-callback-ref@1.3.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/unpipe@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/unpipe@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tapable@2.2.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tapable@2.2.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/symbol/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/symbol/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time-format/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time-format/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-timer/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-timer/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/scripts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/use-sidecar@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/use-sidecar@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/pl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/pl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-ease/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-ease/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/utils-merge@1.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/utils-merge@1.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tsx@4.19.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tsx@4.19.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/init/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/init/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/cjs/use-sync-external-store-shim/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/use-sync-external-store@1.2.2@@@1/cjs/use-sync-external-store-shim/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/offset/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/src/offset/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-color/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-color/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/util-deprecate@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/util-deprecate@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/which@2.0.2@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/which@2.0.2@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/nesting/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/nesting/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-array/src/threshold/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-array/src/threshold/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/ru/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/ru/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-voronoi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-voronoi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-ease/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-ease/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time-format/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time-format/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/postcss-plugins/nesting/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/postcss-plugins/nesting/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/ko/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/ko/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-color/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-color/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ts-interface-checker@0.1.13@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ts-interface-checker@0.1.13@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-interpolate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-interpolate/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/internmap/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/internmap/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/help/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/src/cli/help/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-timer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-timer/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/ja/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/ja/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tiny-invariant@1.3.3@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tiny-invariant@1.3.3@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-time/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/zh-cn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/zh-cn/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-array/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-array/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/zh-tw/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/zh-tw/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/victory-vendor@36.9.2@@@1/lib-vendor/d3-shape/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/typescript@5.6.3@@@1/lib/pt-br/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/typescript@5.6.3@@@1/lib/pt-br/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-refresh@0.14.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-refresh@0.14.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/component/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/component/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/cartesian/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/cartesian/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-remove-scroll@2.6.3@@@1/sidecar/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-remove-scroll@2.6.3@@@1/sidecar/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/io5/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/io5/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/source-map-js@1.2.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/source-map-js@1.2.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/scheduler@0.23.2@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/scheduler@0.23.2@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/scheduler@0.23.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/scheduler@0.23.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/chart/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/chart/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss-animate@1.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss-animate@1.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/source-map-support@0.5.21@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/source-map-support@0.5.21@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/source-map@0.6.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/source-map@0.6.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-dom@18.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-dom@18.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/regenerator-runtime@0.14.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/regenerator-runtime@0.14.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-dom@18.3.1@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-dom@18.3.1@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/chart/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/chart/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/polar/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/polar/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-smooth@4.0.4@@@1/es6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-smooth@4.0.4@@@1/es6/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/chart/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/chart/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/source-map-js@1.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/source-map-js@1.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/es6/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/es6/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/pathfilter/deep_ref/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/pathfilter/deep_ref/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/shebang-regex@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/shebang-regex@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/dotdot/abc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/dotdot/abc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react@18.3.1@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react@18.3.1@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Navigation/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Navigation/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/setprototypeof@1.2.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/setprototypeof@1.2.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/serve-static@1.16.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/serve-static@1.16.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/set-function-length@1.2.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/set-function-length@1.2.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/reusify@1.0.4@@@1/benchmarks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/reusify@1.0.4@@@1/benchmarks/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/shape/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/shape/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/context/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/context/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-smooth@4.0.4@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-smooth@4.0.4@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/signal-exit@4.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/signal-exit@4.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/component/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/component/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/false_main/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/false_main/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useDayRender/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useDayRender/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useSelectedDays/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useSelectedDays/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/nested_symlinks/mylib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/nested_symlinks/mylib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/quux/foo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/quux/foo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-hook-form@7.55.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-hook-form@7.55.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useDayEventHandlers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useDayEventHandlers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/split2@4.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/split2@4.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/strip-ansi@7.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/strip-ansi@7.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/sucrase@3.35.0@@@1/register/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/sucrase@3.35.0@@@1/register/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/cartesian/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/cartesian/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useActiveModifiers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useActiveModifiers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useId/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useId/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/gr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/gr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/rollup@4.24.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/rollup@4.24.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/strip-ansi@6.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/strip-ansi@6.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/same_names/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/same_names/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/payload/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/payload/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/supports-preserve-symlinks-flag@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/supports-preserve-symlinks-flag@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useInput/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useInput/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/tfi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/tfi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-is@16.13.1@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-is@16.13.1@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/vsc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/vsc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/browser_field/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/browser_field/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/statuses@2.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/statuses@2.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/container/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/container/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/source-map@0.6.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/source-map@0.6.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/component/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/component/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-smooth@4.0.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-smooth@4.0.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/go/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/go/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/pi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/pi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-dom@18.3.1@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-dom@18.3.1@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/container/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/container/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-smooth@4.0.4@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-smooth@4.0.4@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-refresh@0.14.2@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-refresh@0.14.2@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwind-merge@2.6.0@@@1/src/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwind-merge@2.6.0@@@1/src/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/semver@6.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/semver@6.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/fc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/fc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/cursor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/cursor/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/y/ccc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/y/ccc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/gi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/gi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/wi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/wi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/reusify@1.0.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/reusify@1.0.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/side-channel@1.0.6@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/side-channel@1.0.6@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/dot_main/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/dot_main/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/rx/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/rx/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/ci/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/ci/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react@18.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react@18.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/sl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/sl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/polar/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/polar/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/safe-buffer@5.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/safe-buffer@5.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/shape/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/shape/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/module_dir/zmodules/bbb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/module_dir/zmodules/bbb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/TransitionGroup/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/TransitionGroup/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwind-merge@2.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwind-merge@2.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/symlinked/_/symlink_target/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/symlinked/_/symlink_target/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/container/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/container/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/baz/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/baz/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/module_dir/xmodules/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/module_dir/xmodules/aaa/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/di/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/di/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/sucrase@3.35.0@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/sucrase@3.35.0@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useControlledValue/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useControlledValue/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/numberAxis/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/numberAxis/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/polar/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/polar/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/cartesian/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/cartesian/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/ti/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/ti/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/side-channel@1.0.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/side-channel@1.0.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectSingle/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectSingle/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/bs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/bs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/send@0.19.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/send@0.19.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/cursor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/cursor/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/dot_slash_main/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/dot_slash_main/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/shebang-command@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/shebang-command@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-is@18.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-is@18.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/esm/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/esm/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-is@16.13.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-is@16.13.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/safer-buffer@2.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/safer-buffer@2.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/run-parallel@1.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/run-parallel@1.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/shape/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/shape/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useDayRender/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useDayRender/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/incorrect_main/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/incorrect_main/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/multirepo/packages/package-b/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/multirepo/packages/package-b/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-remove-scroll@2.6.3@@@1/UI/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-remove-scroll@2.6.3@@@1/UI/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/read-cache@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/read-cache@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/build/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/build/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/CSSTransition/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/CSSTransition/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/help/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/help/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/string-width@5.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/string-width@5.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectRange/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectRange/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/readdirp@3.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/readdirp@3.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react@18.3.1@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react@18.3.1@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/x/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/x/aaa/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/invalid_main/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/invalid_main/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-remove-scroll@2.6.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-remove-scroll@2.6.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/md/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/md/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/supports-preserve-symlinks-flag@1.0.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/supports-preserve-symlinks-flag@1.0.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-remove-scroll-bar@2.3.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-remove-scroll-bar@2.3.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/regexparam@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/regexparam@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/cjs/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/cjs/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/hi2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/hi2/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/example/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/example/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/context/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/context/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useInput/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/hooks/useInput/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/config/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/config/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/tooltip/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/util/tooltip/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/css/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/css/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/string-width@4.2.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/string-width@4.2.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/set-function-length@1.2.2@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/set-function-length@1.2.2@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Navigation/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Navigation/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/setprototypeof@1.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/setprototypeof@1.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/fa/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/fa/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/lia/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/lia/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/SwitchTransition/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/SwitchTransition/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/init/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/init/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/tooltip/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/tooltip/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/TransitionGroupContext/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/TransitionGroupContext/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/ReplaceTransition/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/ReplaceTransition/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-is@18.3.1@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-is@18.3.1@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectMultiple/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectMultiple/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/bi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/bi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/same_names/foo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/same_names/foo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/cg/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/cg/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/cursor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/cursor/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/io/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/io/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/multirepo/packages/package-a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/multirepo/packages/package-a/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/tb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/tb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/fi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/fi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-is@16.13.1@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-is@16.13.1@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/si/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/si/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectRange/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/SelectRange/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/multirepo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/multirepo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-resizable-panels@2.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-resizable-panels@2.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/dotdot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/dotdot/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve-pkg-maps@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve-pkg-maps@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/precedence/bbb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/precedence/bbb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/ri/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/ri/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/semver@6.3.1@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/semver@6.3.1@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/sucrase@3.35.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/sucrase@3.35.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/tooltip/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/tooltip/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/lu/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/lu/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/symlinked/package/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/symlinked/package/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/fa6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/fa6/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/hi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/hi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/es6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/es6/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/other_path/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/other_path/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/im/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/im/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-icons@5.4.0@@@1/ai/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-icons@5.4.0@@@1/ai/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/scheduler@0.23.2@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/scheduler@0.23.2@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/x/ccc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/x/ccc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/precedence/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/precedence/aaa/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/without_basedir/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/without_basedir/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/src/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/src/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/module_dir/ymodules/aaa/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/module_dir/ymodules/aaa/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-style-singleton@2.2.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-style-singleton@2.2.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/lib/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/lib/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/y/bbb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/node_path/y/bbb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/other_path/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/resolver/other_path/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/es6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/es6/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-transition-group@4.4.5@@@1/Transition/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-transition-group@4.4.5@@@1/Transition/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts-scale@0.4.5@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts-scale@0.4.5@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/resolve@1.22.8@@@1/test/precedence/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/resolve@1.22.8@@@1/test/precedence/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwind-merge@2.6.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwind-merge@2.6.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/sucrase@3.35.0@@@1/ts-node-plugin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/sucrase@3.35.0@@@1/ts-node-plugin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-smooth@4.0.4@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-smooth@4.0.4@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/context/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/context/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/tailwindcss@3.4.17@@@1/lib/cli/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-remove-scroll-bar@2.3.8@@@1/constants/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-remove-scroll-bar@2.3.8@@@1/constants/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/payload/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/util/payload/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/supports-preserve-symlinks-flag@1.0.0@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/supports-preserve-symlinks-flag@1.0.0@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-is@18.3.1@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-is@18.3.1@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/numberAxis/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/numberAxis/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/payload/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/lib/util/payload/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/recharts@2.15.2@@@1/types/numberAxis/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/recharts@2.15.2@@@1/types/numberAxis/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/side-channel@1.0.6@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/side-channel@1.0.6@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/mime-types@2.1.35@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/mime-types@2.1.35@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/node-releases@2.0.18@@@1/data/release-schedule/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/node-releases@2.0.18@@@1/data/release-schedule/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/picocolors@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/picocolors@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lodash@4.17.21@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lodash@4.17.21@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/mime-db@1.52.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/mime-db@1.52.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/qs@6.13.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/qs@6.13.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-interval@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-interval@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/minipass@7.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/minipass@7.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/CaptionNavigation/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/CaptionNavigation/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/negotiator@0.6.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/negotiator@0.6.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-js@4.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-js@4.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-range@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-range@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-utils@11.13.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-utils@11.13.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-hash@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-hash@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/labels/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/labels/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss@8.4.47@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss@8.4.47@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/minimatch@9.0.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/minimatch@9.0.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-import@15.1.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-import@15.1.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/on-finished@2.4.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/on-finished@2.4.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-protocol@1.7.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-protocol@1.7.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Head/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Head/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-pool@3.7.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-pool@3.7.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/obuf@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/obuf@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pause@0.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pause@0.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-bytea@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-bytea@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/nanoid@3.3.8@@@1/async/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/nanoid@3.3.8@@@1/async/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-types@4.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-types@4.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss@8.4.47@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss@8.4.47@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/parseurl@1.3.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/parseurl@1.3.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-nested@6.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-nested@6.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/methods@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/methods@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-types@2.2.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-types@2.2.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-protocol@1.7.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-protocol@1.7.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-pool@3.7.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-pool@3.7.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Month/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Month/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/mitt@3.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/mitt@3.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/formatters/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/formatters/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Table/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Table/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/mz@2.7.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/mz@2.7.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-bytea@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-bytea@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/proxy-addr@2.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/proxy-addr@2.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Modifiers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Modifiers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/prop-types@15.8.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/prop-types@15.8.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/nanoid@3.3.8@@@1/non-secure/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/nanoid@3.3.8@@@1/non-secure/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/next-themes@0.4.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/next-themes@0.4.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-import@15.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-import@15.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-inspect@1.13.2@@@1/example/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-inspect@1.13.2@@@1/example/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/negotiator@0.6.3@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/negotiator@0.6.3@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/MonthsDropdown/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/MonthsDropdown/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/raw-body@2.5.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/raw-body@2.5.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-load-config@4.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-load-config@4.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport-local@1.0.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport-local@1.0.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/media-typer@0.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/media-typer@0.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/IconLeft/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/IconLeft/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/prop-types@15.8.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/prop-types@15.8.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/IconDropdown/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/IconDropdown/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ms@2.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ms@2.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/package-json-from-dist@1.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/package-json-from-dist@1.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/normalize-path@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/normalize-path@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-selector-parser@6.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-selector-parser@6.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/modern-screenshot@4.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/modern-screenshot@4.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/mime@1.6.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/mime@1.6.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/gestures/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/gestures/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-protocol@1.7.0@@@1/src/testing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-protocol@1.7.0@@@1/src/testing/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg@8.13.1@@@1/lib/crypto/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg@8.13.1@@@1/lib/crypto/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/random-bytes@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/random-bytes@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ms@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ms@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pseudomap@1.0.2@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pseudomap@1.0.2@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-inspect@1.13.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-inspect@1.13.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/picomatch@2.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/picomatch@2.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-value-parser@4.2.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-value-parser@4.2.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lru-cache@5.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lru-cache@5.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/node-gyp-build@4.8.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/node-gyp-build@4.8.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Navigation/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Navigation/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/node-releases@2.0.18@@@1/data/processed/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/node-releases@2.0.18@@@1/data/processed/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-inspect@1.13.2@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-inspect@1.13.2@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pseudomap@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pseudomap@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/memorystore@1.6.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/memorystore@1.6.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Row/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Row/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/mime@1.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/mime@1.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-assign@4.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-assign@4.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-utils@11.13.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-utils@11.13.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/micromatch@4.0.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/micromatch@4.0.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/WeekNumber/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/WeekNumber/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Footer/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Footer/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Dropdown/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Dropdown/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/path-to-regexp@0.1.12@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/path-to-regexp@0.1.12@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-interval@1.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-interval@1.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/path-key@3.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/path-key@3.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/gestures/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/gestures/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Modifiers/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Modifiers/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-types@2.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-types@2.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-cloudflare@1.1.1@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-cloudflare@1.1.1@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/qs@6.13.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/qs@6.13.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-types@4.0.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-types@4.0.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/DayContent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/DayContent/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-utils@11.13.0@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-utils@11.13.0@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/nanoid@3.3.8@@@1/url-alphabet/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/nanoid@3.3.8@@@1/url-alphabet/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-selector-parser@6.0.10@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-selector-parser@6.0.10@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/merge-descriptors@1.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/merge-descriptors@1.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-array@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-array@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lucide-react@0.453.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lucide-react@0.453.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg@8.13.1@@@1/lib/native/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg@8.13.1@@@1/lib/native/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lru-cache@10.4.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lru-cache@10.4.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pirates@4.0.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pirates@4.0.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/gestures/drag/state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/gestures/drag/state/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-date@1.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-date@1.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/MonthsDropdown/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/MonthsDropdown/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/picomatch@2.3.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/picomatch@2.3.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg@8.13.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg@8.13.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg@8.13.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg@8.13.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/normalize-range@0.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/normalize-range@0.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-connection-string@2.7.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-connection-string@2.7.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/qs@6.13.0@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/qs@6.13.0@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pgpass@1.0.5@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pgpass@1.0.5@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/on-headers@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/on-headers@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/etc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/etc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/lib/strategies/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/lib/strategies/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Button/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Button/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/HeadRow/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/HeadRow/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/nanoid@3.3.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/nanoid@3.3.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport-strategy@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport-strategy@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-numeric@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-numeric@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lru-cache@4.1.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lru-cache@4.1.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/gestures/drag/state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/gestures/drag/state/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Focus/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Focus/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/sponsors/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/sponsors/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pify@2.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pify@2.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/loose-envify@1.4.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/loose-envify@1.4.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-int8@1.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-int8@1.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/types/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-date@2.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-date@2.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-load-config@4.0.2@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-load-config@4.0.2@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Caption/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Caption/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/gestures/drag/state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/gestures/drag/state/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/magic-string@0.30.17@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/magic-string@0.30.17@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/queue-microtask@1.2.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/queue-microtask@1.2.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Table/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/HeadRow/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/HeadRow/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Table/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Table/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/lib/middleware/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/lib/middleware/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pirates@4.0.6@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pirates@4.0.6@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/gestures/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/gestures/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-utils@11.13.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-utils@11.13.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Months/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Months/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/range-parser@1.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/range-parser@1.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/node-releases@2.0.18@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/node-releases@2.0.18@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/IconRight/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/IconRight/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/WeekNumber/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/WeekNumber/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Focus/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/Focus/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/src/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postgres-array@3.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postgres-array@3.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/postcss-value-parser@4.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/postcss-value-parser@4.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Day/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Day/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-protocol@1.7.0@@@1/src/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-protocol@1.7.0@@@1/src/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pgpass@1.0.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pgpass@1.0.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-inspect@1.13.2@@@1/test/browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-inspect@1.13.2@@@1/test/browser/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-types@2.2.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-types@2.2.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/contexts/DayPicker/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/YearsDropdown/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/YearsDropdown/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/CaptionDropdowns/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/CaptionDropdowns/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/nanoid@3.3.8@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/nanoid@3.3.8@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/pg-cloudflare@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/pg-cloudflare@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/path-scurry@1.11.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/path-scurry@1.11.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/YearsDropdown/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/YearsDropdown/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/qs@6.13.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/qs@6.13.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/merge2@1.4.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/merge2@1.4.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/path-parse@1.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/path-parse@1.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Root/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/Root/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/CaptionLabel/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/react-day-picker@8.10.1@@@1/src/components/CaptionLabel/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/memorystore@1.6.7@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/memorystore@1.6.7@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-utils@11.13.0@@@1/.turbo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-utils@11.13.0@@@1/.turbo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/obuf@1.1.2@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/obuf@1.1.2@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/lib/framework/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/lib/framework/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport-local@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport-local@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/memorystore@1.6.7@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/memorystore@1.6.7@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/object-inspect@1.13.2@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/object-inspect@1.13.2@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/lib/http/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/lib/http/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport-strategy@1.0.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport-strategy@1.0.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/motion-dom@11.13.0@@@1/.turbo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/motion-dom@11.13.0@@@1/.turbo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/passport@0.7.0@@@1/lib/errors/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/passport@0.7.0@@@1/lib/errors/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lodash@4.17.21@@@1/fp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lodash@4.17.21@@@1/fp/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/json5@2.2.3@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/json5@2.2.3@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lightningcss@1.29.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lightningcss@1.29.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lines-and-columns@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lines-and-columns@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lilconfig@3.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lilconfig@3.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-core-module@2.15.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-core-module@2.15.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jsesc@3.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jsesc@3.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/isexe@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/isexe@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jiti@2.4.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jiti@2.4.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-core-module@2.15.1@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-core-module@2.15.1@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lodash.castarray@4.4.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lodash.castarray@4.4.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lodash.isplainobject@4.0.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lodash.isplainobject@4.0.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-number@7.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-number@7.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lightningcss-linux-x64-gnu@1.29.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lightningcss-linux-x64-gnu@1.29.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jackspeak@3.4.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jackspeak@3.4.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/js-tokens@4.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/js-tokens@4.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jiti@1.21.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jiti@1.21.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lightningcss@1.29.2@@@1/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lightningcss@1.29.2@@@1/node/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jiti@1.21.6@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jiti@1.21.6@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lightningcss-linux-x64-musl@1.29.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lightningcss-linux-x64-musl@1.29.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-fullwidth-code-point@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-fullwidth-code-point@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jiti@1.21.6@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jiti@1.21.6@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/json5@2.2.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/json5@2.2.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lodash.merge@4.6.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lodash.merge@4.6.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-binary-path@2.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-binary-path@2.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-extglob@2.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-extglob@2.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/isexe@2.0.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/isexe@2.0.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lilconfig@3.1.3@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lilconfig@3.1.3@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/is-glob@4.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/is-glob@4.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jiti@2.4.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jiti@2.4.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/lines-and-columns@1.2.4@@@1/build/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/lines-and-columns@1.2.4@@@1/build/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jsesc@3.0.2@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jsesc@3.0.2@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/jsesc@3.0.2@@@1/man/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/jsesc@3.0.2@@@1/man/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ipaddr.js@1.9.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ipaddr.js@1.9.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ipaddr.js@1.9.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ipaddr.js@1.9.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/internmap@2.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/internmap@2.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/internmap@2.0.3@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/internmap@2.0.3@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/input-otp@1.4.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/input-otp@1.4.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/inherits@2.0.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/inherits@2.0.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/iconv-lite@0.4.24@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/iconv-lite@0.4.24@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/iconv-lite@0.4.24@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/iconv-lite@0.4.24@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/iconv-lite@0.4.24@@@1/encodings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/iconv-lite@0.4.24@@@1/encodings/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/iconv-lite@0.4.24@@@1/encodings/tables/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/iconv-lite@0.4.24@@@1/encodings/tables/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/http-errors@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/http-errors@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/hasown@2.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/hasown@2.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/hasown@2.0.2@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/hasown@2.0.2@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-symbols@1.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-symbols@1.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-symbols@1.0.3@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-symbols@1.0.3@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-symbols@1.0.3@@@1/test/shams/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-symbols@1.0.3@@@1/test/shams/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-symbols@1.0.3@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-symbols@1.0.3@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-proto@1.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-proto@1.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-proto@1.0.3@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-proto@1.0.3@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-proto@1.0.3@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-proto@1.0.3@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-property-descriptors@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-property-descriptors@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-property-descriptors@1.0.2@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-property-descriptors@1.0.2@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/has-property-descriptors@1.0.2@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/has-property-descriptors@1.0.2@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/graceful-fs@4.2.11@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/graceful-fs@4.2.11@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/gopd@1.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/gopd@1.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/gopd@1.0.1@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/gopd@1.0.1@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/globals@11.12.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/globals@11.12.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/glob@10.4.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/glob@10.4.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/glob-parent@6.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/glob-parent@6.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/glob-parent@5.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/glob-parent@5.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/get-nonce@1.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/get-nonce@1.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/get-intrinsic@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/get-intrinsic@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/gensync@1.0.0-4049f5e8f1219d89@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/gensync@1.0.0-4049f5e8f1219d89@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/function-bind@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/function-bind@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/function-bind@1.1.2@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/function-bind@1.1.2@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/function-bind@1.1.2@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/function-bind@1.1.2@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fresh@0.5.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fresh@0.5.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/framer-motion@11.13.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/framer-motion@11.13.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/framer-motion@11.13.1@@@1/mini/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/framer-motion@11.13.1@@@1/mini/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/framer-motion@11.13.1@@@1/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/framer-motion@11.13.1@@@1/dom/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fraction.js@4.3.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fraction.js@4.3.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/foreground-child@3.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/foreground-child@3.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/finalhandler@1.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/finalhandler@1.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fastq@1.17.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fastq@1.17.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fastq@1.17.1@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fastq@1.17.1@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/get-intrinsic@1.2.4@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/get-intrinsic@1.2.4@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel@8.6.0@@@1/esm/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel@8.6.0@@@1/esm/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/esm/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/esm/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel@8.6.0@@@1/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel@8.6.0@@@1/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/es-define-property@1.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/es-define-property@1.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/emoji-regex@8.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/emoji-regex@8.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-core/query-builders/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-core/query-builders/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/enhanced-resolve@5.18.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/enhanced-resolve@5.18.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/forwarded@0.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/forwarded@0.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/get-tsconfig@4.8.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/get-tsconfig@4.8.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel@8.6.0@@@1/cjs/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel@8.6.0@@@1/cjs/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/escalade@3.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/escalade@3.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/gensync@1.0.0-4049f5e8f1219d89@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/gensync@1.0.0-4049f5e8f1219d89@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/matchers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/matchers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/config/tsconfig/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/config/tsconfig/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/tidb-serverless/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/tidb-serverless/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/emoji-regex@9.2.2@@@1/es2015/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/emoji-regex@9.2.2@@@1/es2015/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.23.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.23.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/es-errors@1.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/es-errors@1.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/xata-http/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/xata-http/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/emoji-regex@9.2.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/emoji-regex@9.2.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/emoji-regex@8.0.0@@@1/es2015/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/emoji-regex@8.0.0@@@1/es2015/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/encodeurl@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/encodeurl@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/recipes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/recipes/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/eastasianwidth@0.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/eastasianwidth@0.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/vercel-postgres/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/vercel-postgres/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-core/columns/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-core/columns/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/config/rollup/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/config/rollup/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-zod@0.7.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-zod@0.7.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.19.12@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.19.12@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/encodeurl@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/encodeurl@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.18.20@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.18.20@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/express@4.21.2@@@1/lib/router/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/express@4.21.2@@@1/lib/router/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel@8.6.0@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel@8.6.0@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fill-range@7.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fill-range@7.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/framer-motion@11.13.1@@@1/m/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/framer-motion@11.13.1@@@1/m/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/express-session@1.18.1@@@1/session/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/express-session@1.18.1@@@1/session/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.25.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.25.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/express@4.21.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/express@4.21.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/express@4.21.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/express@4.21.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/scripts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/transformers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/transformers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/eventemitter3@4.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/eventemitter3@4.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/enhanced-resolve@5.18.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/enhanced-resolve@5.18.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/framer-motion@11.13.1@@@1/dom/mini/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/framer-motion@11.13.1@@@1/dom/mini/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ee-first@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ee-first@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/framer-motion@11.13.1@@@1/client/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/framer-motion@11.13.1@@@1/client/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.25.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.25.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/express-session@1.18.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/express-session@1.18.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/express@4.21.2@@@1/lib/middleware/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/express@4.21.2@@@1/lib/middleware/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/enhanced-resolve@5.18.1@@@1/lib/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/enhanced-resolve@5.18.1@@@1/lib/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/es-define-property@1.0.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/es-define-property@1.0.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/readers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/readers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/electron-to-chromium@1.5.51@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/electron-to-chromium@1.5.51@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/filters/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/providers/filters/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/supabase/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/supabase/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/eventemitter3@4.0.7@@@1/umd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/eventemitter3@4.0.7@@@1/umd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.21.5@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.21.5@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/gopd@1.0.1@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/gopd@1.0.1@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-proxy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-proxy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/get-intrinsic@1.2.4@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/get-intrinsic@1.2.4@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel@8.6.0@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel@8.6.0@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.23.1@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.23.1@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fastq@1.17.1@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fastq@1.17.1@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.19.12@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.19.12@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/cjs/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/cjs/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/es-errors@1.3.0@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/es-errors@1.3.0@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/escape-html@1.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/escape-html@1.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.21.5@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.21.5@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.18.20@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.18.20@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.21.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.21.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fastq@1.17.1@@@1/.github/workflows/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fastq@1.17.1@@@1/.github/workflows/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild-register@3.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild-register@3.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.18.20@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.18.20@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/etag@1.8.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/etag@1.8.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-core/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sqlite-core/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-equals@5.2.2@@@1/config/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-equals@5.2.2@@@1/config/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel@8.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel@8.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.19.12@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.19.12@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/escalade@3.2.0@@@1/sync/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/escalade@3.2.0@@@1/sync/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/es-define-property@1.0.0@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/es-define-property@1.0.0@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/managers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/fast-glob@3.3.2@@@1/out/managers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.25.0@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.25.0@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/esbuild@0.23.1@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/esbuild@0.23.1@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/esm/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-react@8.6.0@@@1/esm/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql/functions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql/functions/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql-js/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/es-errors@1.3.0@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/es-errors@1.3.0@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/cjs/components/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/embla-carousel-reactive-utils@8.6.0@@@1/cjs/components/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-core/columns/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-core/columns/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-core/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-core/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/postgres-js/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/postgres-js/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-proxy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-proxy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-core/query-builders/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-core/query-builders/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/columns/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/columns/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql/expressions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/sql/expressions/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/prisma/mysql/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/prisma/mysql/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/planetscale-serverless/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/planetscale-serverless/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/query-builders/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/query-builders/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-proxy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore-proxy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/prisma/sqlite/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/prisma/sqlite/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pglite/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pglite/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/singlestore/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/prisma/pg/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/prisma/pg/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/columns/vector_extension/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/columns/vector_extension/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/columns/postgis_extension/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/columns/postgis_extension/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/query-builders/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/pg-core/query-builders/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/op-sqlite/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/op-sqlite/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/node-postgres/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/node-postgres/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/neon-serverless/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/neon-serverless/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/neon-http/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/neon-http/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/neon/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/neon/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql2/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-core/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-core/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/aws-data-api/common/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/aws-data-api/common/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/knex/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/knex/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/bun-sql/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/bun-sql/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/expo-sqlite/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/expo-sqlite/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-core/columns/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-core/columns/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-proxy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-proxy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/offsetParent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/offsetParent/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/aws-data-api/pg/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/aws-data-api/pg/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/width/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/width/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/kysely/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/kysely/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/css/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/css/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/better-sqlite3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/better-sqlite3/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-core/query-builders/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/mysql-core/query-builders/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-kit@0.30.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-kit@0.30.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/durable-sqlite/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/durable-sqlite/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/http/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/http/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollTop/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollTop/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/ownerWindow/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/ownerWindow/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/hasClass/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/hasClass/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/toggleClass/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/toggleClass/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/hyphenate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/hyphenate/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/listen/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/listen/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/filterEventHandler/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/filterEventHandler/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/position/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/position/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/node/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/ownerDocument/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/ownerDocument/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/transitionEnd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/transitionEnd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/nextUntil/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/nextUntil/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/triggerEvent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/triggerEvent/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/querySelectorAll/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/querySelectorAll/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/matches/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/matches/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isWindow/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isWindow/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/height/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/height/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/getComputedStyle/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/getComputedStyle/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/prepend/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/prepend/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isTransform/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isTransform/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/text/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/text/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/cjs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/cjs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/remove/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/remove/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/removeClass/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/removeClass/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isInput/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isInput/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/hyphenateStyle/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/hyphenateStyle/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/getScrollAccessor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/getScrollAccessor/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/siblings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/siblings/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollTo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollTo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isDocument/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isDocument/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/insertAfter/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/insertAfter/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollParent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollParent/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/clear/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/clear/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollLeft/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollLeft/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/offset/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/offset/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollbarSize/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/scrollbarSize/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/collectElements/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/collectElements/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isVisible/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/isVisible/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/parents/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/parents/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/removeEventListener/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/removeEventListener/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/bun-sqlite/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/bun-sqlite/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/decimal.js-light@2.5.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/decimal.js-light@2.5.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dlv@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dlv@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/didyoumean@1.2.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/didyoumean@1.2.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/define-data-property@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/define-data-property@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/detect-node-es@1.1.0@@@1/es5/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/detect-node-es@1.1.0@@@1/es5/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/sqlite3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/sqlite3/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/debug@2.6.9@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/debug@2.6.9@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/depd@2.0.0@@@1/lib/browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/depd@2.0.0@@@1/lib/browser/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/d1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/d1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/debug@4.3.7@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/debug@4.3.7@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/detect-node-es@1.1.0@@@1/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/detect-node-es@1.1.0@@@1/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/debug@4.3.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/debug@4.3.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/detect-libc@2.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/detect-libc@2.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/debug@2.6.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/debug@2.6.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/define-data-property@1.1.4@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/define-data-property@1.1.4@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/closest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/closest/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/camelizeStyle/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/camelizeStyle/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/decimal.js-light@2.5.1@@@1/doc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/decimal.js-light@2.5.1@@@1/doc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/depd@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/depd@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/web/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/web/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/contains/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/contains/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/collectSiblings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/collectSiblings/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/destroy@1.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/destroy@1.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/wasm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/wasm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/detect-node-es@1.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/detect-node-es@1.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/define-data-property@1.1.4@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/define-data-property@1.1.4@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/ws/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/drizzle-orm@0.39.1@@@1/libsql/ws/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/childNodes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/childNodes/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/detect-libc@2.0.3@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/detect-libc@2.0.3@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/addClass/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/addClass/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/childElements/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/childElements/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/attribute/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/attribute/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/addEventListener/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/addEventListener/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/activeElement/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/activeElement/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/camelize/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/camelize/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/parse/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/parse/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/canUseDOM/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/canUseDOM/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/animationFrame/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/animationFrame/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/dom-helpers@5.2.1@@@1/animate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/dom-helpers@5.2.1@@@1/animate/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/parse/_lib/parsers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/parse/_lib/parsers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-HK/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-HK/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-CN/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-CN/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-CN/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-CN/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-HK/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-HK/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz-Cyrl/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz-Cyrl/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz-Cyrl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz-Cyrl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-TW/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-TW/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-TW/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/zh-TW/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/vi/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/vi/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/vi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/vi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sk/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sk/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ug/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ug/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/th/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/th/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/fp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/fp/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sv/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sv/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt-BR/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt-BR/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/th/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/th/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sl/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sl/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/se/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/se/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mk/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mk/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kk/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kk/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl-BE/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl-BE/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/oc/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/oc/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uk/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uk/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mn/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mn/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mt/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mt/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ta/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ta/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/te/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/te/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lv/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lv/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sk/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sk/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ro/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ro/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ko/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ko/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sq/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sq/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lt/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lt/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uk/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uk/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr-Latn/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr-Latn/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ka/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ka/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kn/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kn/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lb/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lb/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nn/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nn/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/km/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/km/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lv/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/te/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/te/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nb/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nb/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt-BR/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pt-BR/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/tr/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/tr/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sq/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sq/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ms/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ms/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ms/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ms/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja-Hira/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja-<PERSON>ra/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it-CH/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it-CH/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mt/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mt/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pl/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pl/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/km/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/km/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ug/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ug/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ko/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ko/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ru/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ru/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/uz/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sv/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/se/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/se/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/tr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/tr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja-Hira/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja-<PERSON>ra/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ru/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ru/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ka/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ka/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ro/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ro/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl-BE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl-BE/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/oc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/oc/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lt/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/lt/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eo/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eo/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it-CH/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it-CH/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/is/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/is/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ta/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ta/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr-Latn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/sr-Latn/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/is/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/is/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-ZA/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-ZA/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/pl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kn/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mn/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-US/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-US/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mk/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/mk/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-US/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-US/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/it/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-ZA/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-ZA/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/nn/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kk/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/kk/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/et/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/et/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gu/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gu/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/id/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/id/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/he/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/he/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/es/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/es/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ca/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ca/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gd/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gd/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gl/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gl/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hu/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hu/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hi/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hi/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/az/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/az/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/da/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/da/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/el/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/el/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/az/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/az/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cs/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cs/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gd/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gd/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fy/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fy/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-TN/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-TN/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CA/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CA/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de-AT/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de-AT/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CH/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CH/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be-tarask/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be-tarask/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hy/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hy/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fa-IR/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fa-IR/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fi/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fi/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cy/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cy/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ckb/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ckb/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bn/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bn/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bs/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bs/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/cy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be-tarask/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be-tarask/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bg/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bg/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hr/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hr/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ht/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ht/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-EG/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-EG/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/he/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/he/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ca/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ca/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hu/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hu/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fa-IR/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fa-IR/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-GB/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-GB/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bn/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bn/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ckb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ckb/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ht/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ht/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CH/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CH/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-TN/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-TN/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gl/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gl/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-NZ/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-NZ/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eu/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eu/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de-AT/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de-AT/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/et/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/et/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-CA/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-CA/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-SA/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-SA/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/id/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/id/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-IN/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-IN/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-IN/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-IN/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/hy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-IE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-IE/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/de/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-SA/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-SA/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-NZ/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-NZ/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CA/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/fr-CA/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eo/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eo/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/be/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-GB/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-GB/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-AU/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-AU/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gu/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/gu/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/da/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/da/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-AU/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-AU/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/es/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/es/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/el/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/el/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ja/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-CA/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/en-CA/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eu/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/eu/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bg/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/bg/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-DZ/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-DZ/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-MA/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-MA/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-MA/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-MA/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-EG/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-EG/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-DZ/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar-DZ/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/ar/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/af/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/af/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/af/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/af/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/locale/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/docs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/docs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/_lib/format/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/_lib/format/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-timer@3.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-timer@3.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-timer@3.0.1@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-timer@3.0.1@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/curve/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/curve/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/date-fns@3.6.0@@@1/fp/_lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/date-fns@3.6.0@@@1/fp/_lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-scale@4.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-scale@4.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/order/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/order/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-scale@4.0.2@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-scale@4.0.2@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/offset/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/offset/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/data/regions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/data/regions/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/chokidar@3.6.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/chokidar@3.6.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/data/features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/data/features/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/class-variance-authority@0.7.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/class-variance-authority@0.7.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/chokidar@3.6.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/chokidar@3.6.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-interpolate@3.0.1@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-interpolate@3.0.1@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-array@3.2.4@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-array@3.2.4@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cookie@0.7.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cookie@0.7.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-path@3.1.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-path@3.1.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-time@3.1.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-time@3.1.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/color-name@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/color-name@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cookie@0.7.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cookie@0.7.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-format@3.1.0@@@1/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-format@3.1.0@@@1/locale/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/color-convert@2.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/color-convert@2.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-interpolate@3.0.1@@@1/src/transform/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-interpolate@3.0.1@@@1/src/transform/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/symbol/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-shape@3.2.0@@@1/src/symbol/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-color@3.1.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-color@3.1.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-shape@3.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-shape@3.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cross-spawn@7.0.6@@@1/lib/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cross-spawn@7.0.6@@@1/lib/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/commander@4.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/commander@4.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-color@3.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-color@3.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/clsx@2.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/clsx@2.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-format@3.1.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-format@3.1.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/autoprefixer@10.4.20@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/autoprefixer@10.4.20@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-array@3.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-array@3.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/content-type@1.0.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/content-type@1.0.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-time-format@4.1.0@@@1/locale/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-time-format@4.1.0@@@1/locale/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/convert-source-map@2.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/convert-source-map@2.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-format@3.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-format@3.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cross-spawn@7.0.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cross-spawn@7.0.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cssesc@3.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cssesc@3.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/caniuse-lite@1.0.30001677@@@1/data/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/commander@4.1.1@@@1/typings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/commander@4.1.1@@@1/typings/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-interpolate@3.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-interpolate@3.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-path@3.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-path@3.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cookie-signature@1.0.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cookie-signature@1.0.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-ease@3.0.1@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-ease@3.0.1@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cookie-signature@1.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cookie-signature@1.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/connect-pg-simple@10.0.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/connect-pg-simple@10.0.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-time@3.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-time@3.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-array@3.2.4@@@1/src/threshold/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-array@3.2.4@@@1/src/threshold/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cmdk@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cmdk@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/content-disposition@0.5.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/content-disposition@0.5.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-time-format@4.1.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-time-format@4.1.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/chokidar@3.6.0@@@1/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/chokidar@3.6.0@@@1/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/autoprefixer@10.4.20@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/autoprefixer@10.4.20@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cross-spawn@7.0.6@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cross-spawn@7.0.6@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/csstype@3.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/csstype@3.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-time-format@4.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-time-format@4.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cssesc@3.0.0@@@1/man/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cssesc@3.0.0@@@1/man/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/cssesc@3.0.0@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/cssesc@3.0.0@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/d3-ease@3.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/d3-ease@3.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ansi-regex@5.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ansi-regex@5.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/accepts@1.3.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/accepts@1.3.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@vitejs/plugin-react@4.3.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@vitejs/plugin-react@4.3.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/ws@8.5.13@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/ws@8.5.13@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/serve-static@1.15.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/serve-static@1.15.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/send@0.17.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/send@0.17.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/react@18.3.12@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/react@18.3.12@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/react@18.3.12@@@1/ts5.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/react@18.3.12@@@1/ts5.0/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/react-dom@18.3.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/react-dom@18.3.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/range-parser@1.2.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/range-parser@1.2.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/qs@6.9.16@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/qs@6.9.16@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/passport-strategy@0.2.38@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/passport-strategy@0.2.38@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/passport-local@1.0.38@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/passport-local@1.0.38@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/estree@1.0.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/estree@1.0.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-timer@3.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-timer@3.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/camelcase-css@2.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/camelcase-css@2.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/call-bind@1.0.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/call-bind@1.0.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/call-bind@1.0.7@@@1/test/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/call-bind@1.0.7@@@1/test/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/call-bind@1.0.7@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/call-bind@1.0.7@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/bytes@3.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/bytes@3.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/bufferutil@4.0.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/bufferutil@4.0.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/bufferutil@4.0.8@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/bufferutil@4.0.8@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/buffer-from@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/buffer-from@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/browserslist@4.24.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/browserslist@4.24.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/braces@3.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/braces@3.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/braces@3.0.3@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/braces@3.0.3@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/brace-expansion@2.0.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/brace-expansion@2.0.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/brace-expansion@2.0.1@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/brace-expansion@2.0.1@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/body-parser@1.20.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/body-parser@1.20.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/body-parser@1.20.3@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/body-parser@1.20.3@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/body-parser@1.20.3@@@1/lib/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/body-parser@1.20.3@@@1/lib/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/binary-extensions@2.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/binary-extensions@2.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/balanced-match@1.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/balanced-match@1.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/balanced-match@1.0.2@@@1/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/balanced-match@1.0.2@@@1/.github/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/autoprefixer@10.4.20@@@1/lib/hacks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/autoprefixer@10.4.20@@@1/lib/hacks/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/autoprefixer@10.4.20@@@1/data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/autoprefixer@10.4.20@@@1/data/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/autoprefixer@10.4.20@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/autoprefixer@10.4.20@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/array-flatten@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/array-flatten@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/aria-hidden@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/aria-hidden@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/arg@5.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/arg@5.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/anymatch@3.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/anymatch@3.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/any-promise@1.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/any-promise@1.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/any-promise@1.3.0@@@1/register/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/any-promise@1.3.0@@@1/register/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ansi-styles@6.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ansi-styles@6.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ansi-styles@4.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ansi-styles@4.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/ansi-regex@6.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/ansi-regex@6.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/legacy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/legacy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/react-dom@18.3.1@@@1/test-utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/react-dom@18.3.1@@@1/test-utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/ts5.6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/ts5.6/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/express-session@1.18.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/express-session@1.18.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/build/legacy/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/build/legacy/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-scale@4.0.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-scale@4.0.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/remove-overloads/transformers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/remove-overloads/transformers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/rename-properties/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/rename-properties/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/http-errors@2.0.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/http-errors@2.0.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/pg@8.11.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/pg@8.11.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-path@3.1.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-path@3.1.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-interpolate@3.0.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-interpolate@3.0.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/stream/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/stream/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-shape@3.1.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-shape@3.1.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/mime@1.3.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/mime@1.3.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/dns/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/dns/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-time@3.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-time@3.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/prop-types@15.7.13@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/prop-types@15.7.13@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/pg@8.11.6@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/pg@8.11.6@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/express@4.17.21@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/express@4.17.21@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/passport@1.0.17@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/passport@1.0.17@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/express-serve-static-core@4.19.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/express-serve-static-core@4.19.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/assert/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/assert/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/fs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/fs/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/readline/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/readline/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/node@20.16.11@@@1/timers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/node@20.16.11@@@1/timers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/remove-overloads/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/remove-overloads/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/remove-overloads/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/remove-overloads/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/rename-hydrate/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/rename-hydrate/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/keep-previous-data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/keep-previous-data/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/modern/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/modern/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/validators/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/validators/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-alert-dialog@1.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-alert-dialog@1.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-collapsible@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-collapsible@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/build/modern/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/build/modern/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/path/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/path/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/modifications/typescript/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/modifications/typescript/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/converters/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/converters/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/comments/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/comments/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@jridgewell/sourcemap-codec@1.5.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@jridgewell/sourcemap-codec@1.5.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/query-core@5.60.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/clone/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/clone/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/flow/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/flow/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/validators/react/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/validators/react/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@neondatabase/serverless@0.10.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@neondatabase/serverless@0.10.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild/linux-x64@0.25.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild/linux-x64@0.25.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/retrievers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/retrievers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/constants/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/constants/generated/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild-kit/core-utils@3.3.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild-kit/core-utils@3.3.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@pkgjs/parseargs@0.11.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@pkgjs/parseargs@0.11.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/modifications/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/modifications/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-array@3.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-array@3.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/readers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/readers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@isaacs/cliui@8.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@isaacs/cliui@8.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@pkgjs/parseargs@0.11.0@@@1/examples/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@pkgjs/parseargs@0.11.0@@@1/examples/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/ast-types/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/ast-types/generated/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-compose-refs@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-compose-refs@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-dropdown-menu@2.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-dropdown-menu@2.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/query-codemods/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/query-codemods/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/body-parser@1.19.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/body-parser@1.19.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-accordion@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-accordion@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@drizzle-team/brocli@0.10.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@drizzle-team/brocli@0.10.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/oxide@4.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/oxide@4.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-id@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-id@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/providers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/adapters/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/adapters/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/babel__traverse@7.20.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/babel__traverse@7.20.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-context@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-context@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/providers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@jridgewell/gen-mapping@0.3.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@jridgewell/gen-mapping@0.3.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/generated/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@floating-ui/utils@0.2.9@@@1/dom/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@floating-ui/utils@0.2.9@@@1/dom/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/definitions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/definitions/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@isaacs/cliui@8.0.2@@@1/build/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@isaacs/cliui@8.0.2@@@1/build/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/babel__core@7.20.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/babel__core@7.20.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-collection@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-collection@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-color@3.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-color@3.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-focus-guards@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-focus-guards@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-alert-dialog@1.1.7@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-alert-dialog@1.1.7@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/connect@3.4.38@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/connect@3.4.38@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@floating-ui/utils@0.2.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@floating-ui/utils@0.2.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild-kit/esm-loader@2.6.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild-kit/esm-loader@2.6.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/babel__template@7.4.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/babel__template@7.4.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-focus-scope@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-focus-scope@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/oxide-linux-x64-gnu@4.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/oxide-linux-x64-gnu@4.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-avatar@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-avatar@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/adapters/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/adapters/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/babel__generator@7.6.8@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/babel__generator@7.6.8@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/scope/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/scope/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/scope/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/scope/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/number@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/number@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-context-menu@2.2.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-context-menu@2.2.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/helpers/esm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/helpers/esm/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-checkbox@1.1.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-checkbox@1.1.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/utils/transformers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/utils/transformers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.stat@2.0.5@@@1/out/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild/linux-x64@0.21.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild/linux-x64@0.21.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/is-loading/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/is-loading/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/primitive@1.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/primitive@1.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@pkgjs/parseargs@0.11.0@@@1/internal/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@pkgjs/parseargs@0.11.0@@@1/internal/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typebox/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@floating-ui/react-dom@2.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@floating-ui/react-dom@2.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/asserts/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/asserts/generated/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-aspect-ratio@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-aspect-ratio@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/modifications/flow/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/modifications/flow/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild/linux-x64@0.19.12@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild/linux-x64@0.19.12@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-toggle-group@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-toggle-group@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/keep-previous-data/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v5/keep-previous-data/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/d3-ease@3.0.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/d3-ease@3.0.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.walk@1.2.8@@@1/out/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/typescript/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/typescript/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/traverse/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/traverse/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@isaacs/cliui@8.0.2@@@1/build/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@isaacs/cliui@8.0.2@@@1/build/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/yup/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v4/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v4/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@types/connect-pg-simple@7.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@types/connect-pg-simple@7.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/helpers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-dialog@1.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-dialog@1.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild/linux-x64@0.23.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild/linux-x64@0.23.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@jridgewell/set-array@1.2.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@jridgewell/set-array@1.2.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@floating-ui/dom@1.6.13@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@floating-ui/dom@1.6.13@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v4/utils/replacers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/v4/utils/replacers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/constants/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/constants/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/asserts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/asserts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-visually-hidden@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-visually-hidden@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/validators/generated/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/validators/generated/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@jridgewell/trace-mapping@0.3.25@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@jridgewell/trace-mapping@0.3.25@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@rollup/rollup-linux-x64-gnu@4.24.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@rollup/rollup-linux-x64-gnu@4.24.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/computed-types/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-hover-card@1.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-hover-card@1.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-arrow@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-arrow@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-dismissable-layer@1.1.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-dismissable-layer@1.1.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typanion/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/__tests__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/__tests__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/template@7.26.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/template@7.26.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/oxide-linux-x64-musl@4.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/oxide-linux-x64-musl@4.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@esbuild/linux-x64@0.18.20@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@esbuild/linux-x64@0.18.20@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vest/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@jridgewell/resolve-uri@3.1.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@jridgewell/resolve-uri@3.1.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/effect-ts/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-label@2.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-label@2.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/react/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/builders/react/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/zod/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/vite@4.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/vite@4.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/typeschema/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tanstack/react-query@5.60.5@@@1/build/codemods/src/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/fluentvalidation-ts/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/ajv/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/nope/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/arktype/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/superstruct/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/joi/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/io-ts/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/utils/react/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/types@7.26.9@@@1/lib/utils/react/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/__tests__/__fixtures__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/valibot/src/__tests__/__fixtures__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/vine/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/__tests__/__snapshots__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@hookform/resolvers@3.10.0@@@1/class-validator/src/__tests__/__snapshots__/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-direction@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-direction@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@floating-ui/core@1.6.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@floating-ui/core@1.6.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/typography@0.5.15@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/typography@0.5.15@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/typography@0.5.15@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/typography@0.5.15@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@tailwindcss/node@4.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@tailwindcss/node@4.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@rollup/rollup-linux-x64-musl@4.24.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@rollup/rollup-linux-x64-musl@4.24.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@replit/vite-plugin-runtime-error-modal@0.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@replit/vite-plugin-runtime-error-modal@0.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@replit/vite-plugin-cartographer@0.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@replit/vite-plugin-cartographer@0.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/rect@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/rect@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-size@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-size@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-rect@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-rect@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-previous@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-previous@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-layout-effect@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-layout-effect@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-escape-keydown@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-escape-keydown@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-controllable-state@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-controllable-state@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-use-callback-ref@1.1.1@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-use-callback-ref@1.1.1@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-tooltip@1.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-tooltip@1.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-toggle@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-toggle@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-toast@1.2.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-toast@1.2.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-tabs@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-tabs@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-switch@1.1.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-switch@1.1.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-slot@1.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-slot@1.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-slider@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-slider@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-separator@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-separator@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-select@2.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-select@2.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-scroll-area@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-scroll-area@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-roving-focus@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-roving-focus@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-radio-group@1.2.4@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-radio-group@1.2.4@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-progress@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-progress@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-primitive@2.0.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-primitive@2.0.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-presence@1.1.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-presence@1.1.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-portal@1.1.5@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-portal@1.1.5@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-popper@1.2.3@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-popper@1.2.3@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-popover@1.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-popover@1.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-navigation-menu@1.2.6@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-navigation-menu@1.2.6@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-menubar@1.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-menubar@1.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@radix-ui/react-menu@2.1.7@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@radix-ui/react-menu@2.1.7@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/parser@7.26.9@@@1/typings/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/parser@7.26.9@@@1/typings/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-kv-store/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-kv-store/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helpers@7.26.0@@@1/lib/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helpers@7.26.0@@@1/lib/helpers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/types/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/types/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/providers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@nodelib/fs.scandir@2.1.5@@@1/out/providers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/path/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/path/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/path/inference/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/traverse@7.26.9@@@1/lib/path/inference/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/template@7.26.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/template@7.26.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/regenerator/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/runtime@7.27.0@@@1/regenerator/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/plugin-transform-react-jsx-source@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/plugin-transform-react-jsx-source@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/plugin-transform-react-jsx-source@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/plugin-transform-react-jsx-source@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/plugin-transform-react-jsx-self@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/plugin-transform-react-jsx-self@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/plugin-transform-react-jsx-self@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/plugin-transform-react-jsx-self@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/parser@7.26.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/parser@7.26.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-validator-option@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-validator-option@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-validator-option@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-validator-option@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-validator-identifier@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-validator-identifier@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-validator-identifier@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-validator-identifier@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-string-parser@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-string-parser@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-string-parser@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-string-parser@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-plugin-utils@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-plugin-utils@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-plugin-utils@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-plugin-utils@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-module-transforms@7.26.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-module-transforms@7.26.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-module-transforms@7.26.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-module-transforms@7.26.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-module-imports@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-module-imports@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-module-imports@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-module-imports@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-compilation-targets@7.25.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-compilation-targets@7.25.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helper-compilation-targets@7.25.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helper-compilation-targets@7.25.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/generator@7.26.9@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/generator@7.26.9@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/generator@7.26.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/generator@7.26.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/generator@7.26.9@@@1/lib/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/generator@7.26.9@@@1/lib/node/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/generator@7.26.9@@@1/lib/generators/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/generator@7.26.9@@@1/lib/generators/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/src/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/src/config/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/src/config/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/src/config/files/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/src/config/files/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/parser@7.26.9@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/parser@7.26.9@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/parser@7.26.9@@@1/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/parser@7.26.9@@@1/bin/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helpers@7.26.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helpers@7.26.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/helpers@7.26.0@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/helpers@7.26.0@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/transformation/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/transformation/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/transformation/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/transformation/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/transformation/file/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/transformation/file/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/tools/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/tools/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/parser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/parser/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/parser/util/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/parser/util/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/gensync-utils/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/gensync-utils/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/errors/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/errors/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/validation/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/validation/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/helpers/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/files/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/config/files/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/compat-data@7.26.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/compat-data@7.26.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/compat-data@7.26.2@@@1/data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/compat-data@7.26.2@@@1/data/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/code-frame@7.26.2@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/code-frame@7.26.2@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/code-frame@7.26.2@@@1/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/code-frame@7.26.2@@@1/lib/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@ampproject/remapping@2.3.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@ampproject/remapping@2.3.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@alloc/quick-lru@5.2.0@@@1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@alloc/quick-lru@5.2.0@@@1/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/.tmp/.1864dc443f39f7f9-000001D2.node-gyp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/.tmp/.1864dc443f39f7f9-000001D2.node-gyp/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/home/<USER>/workspace/.cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/vendor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/.bun/install/cache/@babel/core@7.26.0@@@1/lib/vendor/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.html-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.html-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7450d424/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7450d424/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/72f647fe-96ef-4ea6-ba9c-cac9662a143c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/72f647fe-96ef-4ea6-ba9c-cac9662a143c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/ef14671/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/ef14671/"}, "/home/<USER>/workspace/.cache/typescript/5.9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3345b3a3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3345b3a3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-11c57c65/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-11c57c65/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-33a9b5f9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-33a9b5f9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7dc372e2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7dc372e2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4ad6b50e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4ad6b50e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/79f31c53/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/79f31c53/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T193725/exthost2/vscode.css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/b74cb05/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/b74cb05/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-444fb6ce/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-444fb6ce/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/55cae083/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/55cae083/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3793598f/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3793598f/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6f9002f1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6f9002f1/"}, "/home/<USER>/workspace/server/migrations/": {"rootPath": "/home/<USER>/workspace", "relPath": "server/migrations/"}, "/home/<USER>/workspace/migrations/": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/"}, "/home/<USER>/workspace/migrations/meta/": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/meta/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2a749f84/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2a749f84/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c52f8ff1-01c7-44e1-b68e-3a8bc3670d83/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c52f8ff1-01c7-44e1-b68e-3a8bc3670d83/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/15548191/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/15548191/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-80b2489/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-80b2489/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/a56240a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/a56240a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6569bc45/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6569bc45/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1490a843/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1490a843/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/472da3d0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/472da3d0/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7150cd1c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7150cd1c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-70093fd2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-70093fd2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-c72094c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-c72094c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-306831fa/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-306831fa/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/output_logging_20250913T234841/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/output_logging_20250913T234841/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedProfilesData/__default__profile__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedProfilesData/__default__profile__/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/output_logging_20250913T234910/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/output_logging_20250913T234910/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250913T234838/exthost2/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/8787316d-d72a-438c-8b9a-170b13242c24/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/8787316d-d72a-438c-8b9a-170b13242c24/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/output_logging_20250914T000234/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/output_logging_20250914T000234/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1a19f2f6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1a19f2f6/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-54ed4403/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-54ed4403/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.html-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T000219/exthost1/vscode.html-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/output_logging_20250914T090937/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/output_logging_20250914T090937/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/output_logging_20250914T091003/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/output_logging_20250914T091003/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T090934/exthost2/vscode.css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/a3ad8dcd-ca7e-4918-af99-cf92ab8e7714/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/e83202c241276a51733e7c39d6932f35/Augment.vscode-augment/augment-user-assets/checkpoint-documents/a3ad8dcd-ca7e-4918-af99-cf92ab8e7714/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7dbe887a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7dbe887a/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/51be22c6/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/51be22c6/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2411a4f1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2411a4f1/"}, "/home/<USER>/workspace/plugins/squadjs/SquadJS-Cheater-Detection-master/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/SquadJS-Cheater-Detection-master/"}, "/home/<USER>/workspace/plugins/squadjs/SquadJS-Cheater-Detection-master/squad-server/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/SquadJS-Cheater-Detection-master/squad-server/utils/"}, "/home/<USER>/workspace/plugins/squadjs/SquadJS-Cheater-Detection-master/squad-server/plugins/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/SquadJS-Cheater-Detection-master/squad-server/plugins/"}, "/home/<USER>/workspace/plugins/squadjs/Squad-Log-To-Graph-master/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/Squad-Log-To-Graph-master/"}, "/home/<USER>/workspace/plugins/squadjs/Squad-Log-To-Graph-master/services/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/Squad-Log-To-Graph-master/services/"}, "/home/<USER>/workspace/plugins/squadjs/Squad-Log-To-Graph-master/output-graphs/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/Squad-Log-To-Graph-master/output-graphs/"}, "/home/<USER>/workspace/plugins/squadjs/Squad-Log-To-Graph-master/input-logs/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/Squad-Log-To-Graph-master/input-logs/"}, "/home/<USER>/workspace/plugins/squadjs/Squad-Log-To-Graph-master/chart-plugins/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/Squad-Log-To-Graph-master/chart-plugins/"}, "/home/<USER>/workspace/plugins/squadjs/Squad-Log-To-Graph-master/chart-functions/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/Squad-Log-To-Graph-master/chart-functions/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/output_logging_20250914T154422/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/output_logging_20250914T154422/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/output_logging_20250914T154441/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/output_logging_20250914T154441/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250914T154418/exthost2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250914T154418/exthost2/Augment.vscode-augment/"}, "/home/<USER>/workspace/plugins/squadjs/": {"rootPath": "/home/<USER>/workspace", "relPath": "plugins/squadjs/"}}